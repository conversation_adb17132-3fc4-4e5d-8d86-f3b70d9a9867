from src.process_event import process_payload

payload = {'projectId': 'eb4e1e72-aa60-4998-acc5-daba841973af', 'jobId': '878e8db8-f8e4-4d66-b292-2e1abfa8ee8a', 'phase': 'CODE_GENERATION', 
           'status': 'IN_PROGRESS', 'user_id': '04815901-b9dd-4fc6-ada2-92c1f7f8f376', 'tech_spec_id': '94b4b717-c13d-4228-aa18-704c6731584a', 
           'code_gen_id': 'beefc7be-f97b-421c-804f-6de010770e2a', 'metadata': {'propagate': True, 'repo_name': 'ux-walkthrough-uy0hvd'}}

if __name__ == '__main__':
    process_payload(payload)
